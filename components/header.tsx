"use client"
import { useState, useRef, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Plus, Menu, X, User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search } from "@/components/search"
import { createSupabaseClient } from "@/lib/supabase/client"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface HeaderProps {
  onSearch?: (query: string) => void
}

export function Header({ onSearch }: HeaderProps) {
  const pathname = usePathname()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [searchExpanded, setSearchExpanded] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const logoRef = useRef<HTMLDivElement>(null)
  
  // Check if user is logged in
  useEffect(() => {
    const checkUser = async () => {
      try {
        const supabase = createSupabaseClient()
        const { data: { session } } = await supabase.auth.getSession()
        setUser(session?.user || null)
      } catch (error) {
        console.error("Error checking auth status:", error)
      } finally {
        setIsLoading(false)
      }
    }
    
    checkUser()
    
    // Set up auth state change listener
    const supabase = createSupabaseClient()
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user || null)
      }
    )
    
    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const isActive = (path: string) => {
    return pathname === path
  }

  // Function to handle search expansion state
  const handleSearchExpand = (expanded: boolean) => {
    setSearchExpanded(expanded)
  }

  return (
    <header className="sticky top-0 z-50 w-full bg-white/80 backdrop-blur-md py-3 border-b border-gray-100">
      <div className="container flex items-center justify-between">
        {/* Logo */}
        <div ref={logoRef} className="flex items-center">
          <Link href="/" className="flex items-center">
            <Image src="/logo.svg" alt="Introducing.day" width={30} height={23} />
          </Link>
        </div>

        {/* Desktop navigation */}
        <nav aria-label="Primary navigation" className="hidden md:flex bg-white/90 backdrop-blur-sm rounded-full p-1 items-center absolute left-1/2 transform -translate-x-1/2 border border-gray-200 shadow-sm">
          <Link
            href="/"
            aria-current={isActive("/") ? "page" : undefined}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              isActive("/") ? "bg-gray-100" : "hover:bg-gray-50"
            }`}
          >
            Explore
          </Link>
          <Link
            href="/collection"
            aria-current={isActive("/collection") ? "page" : undefined}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              isActive("/collection") ? "bg-gray-100" : "hover:bg-gray-50"
            }`}
          >
            Collections
          </Link>
          <Link
            href="/story"
            aria-current={isActive("/story") ? "page" : undefined}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              isActive("/story") ? "bg-gray-100" : "hover:bg-gray-50"
            }`}
          >
            Story
          </Link>
        </nav>

        {/* Desktop actions */}
        <div className="hidden md:flex items-center gap-4">
          <Search onSearch={onSearch} onExpandChange={handleSearchExpand} />

          <Button asChild variant="outline" className="rounded-full border-gray-200 hover:bg-gray-50 gap-1 px-4 h-10">
            <Link href="https://tally.so/r/wLaKdp">
              <Plus className="h-4 w-4" />
              <span>Submit</span>
            </Link>
          </Button>
          
          {!isLoading && (
            user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full h-10 w-10 p-0 border border-gray-200">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={user?.user_metadata?.avatar_url} alt={user?.email || "User"} />
                      <AvatarFallback>{user?.email?.charAt(0).toUpperCase() || "U"}</AvatarFallback>
                    </Avatar>
                    <span className="sr-only">User menu</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="cursor-pointer">
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/profile?tab=settings" className="cursor-pointer">
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/profile?tab=upvoted" className="cursor-pointer">
                      Upvoted Products
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-red-500 focus:text-red-500"
                    onClick={async () => {
                      const supabase = createSupabaseClient()
                      await supabase.auth.signOut()
                      window.location.href = "/"
                    }}
                  >
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button asChild variant="outline" className="rounded-full border-gray-200 hover:bg-gray-50 gap-1 px-4 h-10">
                <Link href="/signin">
                  <User className="h-4 w-4" />
                  <span>Sign In</span>
                </Link>
              </Button>
            )
          )}
        </div>

        {/* Mobile actions - all grouped on the right side initially */}
        <div className="md:hidden flex items-center gap-2 relative">
          {searchExpanded ? (
            <div className="absolute right-full mr-2 transition-all duration-200">
              <button
                className="flex items-center justify-center w-10 h-10 rounded-full border border-gray-200"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X size={18} /> : <Menu size={18} />}
              </button>
            </div>
          ) : (
            <button
              className="flex items-center justify-center w-10 h-10 rounded-full border border-gray-200"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X size={18} /> : <Menu size={18} />}
            </button>
          )}

          {/* Search button */}
          <Search onSearch={onSearch} onExpandChange={handleSearchExpand} />

          {/* Submit button - hidden when search is expanded */}
          {!searchExpanded && (
            <>
              <Button
                asChild
                variant="outline"
                size="sm"
                className="rounded-full border-gray-200 hover:bg-gray-50 w-10 h-10 p-0"
              >
                <Link href="https://tally.so/r/wLaKdp">
                  <Plus className="h-4 w-4" />
                  <span className="sr-only">Submit</span>
                </Link>
              </Button>
              
              {!isLoading && user && (
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="rounded-full border-gray-200 hover:bg-gray-50 w-10 h-10 p-0 ml-2"
                >
                  <Link href="/profile">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.user_metadata?.avatar_url} alt={user?.email || "User"} />
                      <AvatarFallback>{user?.email?.charAt(0).toUpperCase() || "U"}</AvatarFallback>
                    </Avatar>
                    <span className="sr-only">Profile</span>
                  </Link>
                </Button>
              )}
              
              {!isLoading && !user && (
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                  className="rounded-full border-gray-200 hover:bg-gray-50 w-10 h-10 p-0 ml-2"
                >
                  <Link href="/signin">
                    <User className="h-4 w-4" />
                    <span className="sr-only">Sign In</span>
                  </Link>
                </Button>
              )}
            </>
          )}
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white/90 backdrop-blur-md border-b shadow-sm z-50">
          <nav aria-label="Mobile navigation" className="container py-4 flex flex-col">
            <Link
              href="/"
              aria-current={isActive("/") ? "page" : undefined}
              className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
                isActive("/") ? "bg-gray-100" : "hover:bg-gray-50"
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              Explore
            </Link>
            <Link
              href="/collection"
              aria-current={isActive("/collection") ? "page" : undefined}
              className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
                isActive("/collection") ? "bg-gray-100" : "hover:bg-gray-50"
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              Collections
            </Link>
            <Link
              href="/story"
              aria-current={isActive("/story") ? "page" : undefined}
              className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
                isActive("/story") ? "bg-gray-100" : "hover:bg-gray-50"
              }`}
              onClick={() => setMobileMenuOpen(false)}
            >
              Story
            </Link>
            
            {user && (
              <Link
                href="/profile"
                aria-current={isActive("/profile") ? "page" : undefined}
                className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
                  isActive("/profile") ? "bg-gray-100" : "hover:bg-gray-50"
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Profile
              </Link>
            )}
            
            {!user && (
              <Link
                href="/signin"
                aria-current={isActive("/signin") ? "page" : undefined}
                className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
                  isActive("/signin") ? "bg-gray-100" : "hover:bg-gray-50"
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                Sign In
              </Link>
            )}
          </nav>
        </div>
      )}
    </header>
  )
}
