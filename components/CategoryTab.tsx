"use client"

import type React from "react"
import { cn } from "@/lib/utils"
import { FilterIcon } from "lucide-react"

interface CategoryTabProps {
  tabName: string
  icon?: React.ReactNode
  isSelected: boolean
  count?: number
  onClick: () => void
  ref?: React.Ref<HTMLButtonElement> // Changed type from RefObject<HTMLButtonElement> to Ref<HTMLButtonElement>
}

export function CategoryTab({ tabName, icon, isSelected, count, onClick, ref }: CategoryTabProps) {
  return (
    <button
      ref={ref} // Assign ref here
      onClick={onClick}
      className={cn(
        "flex items-center gap-1 md:gap-1.5 px-2 md:px-3 py-2 md:py-2.5 text-xs md:text-sm font-medium transition-colors relative",
        isSelected
          ? "text-zinc-900 border-b-2 border-zinc-900 -mb-px"
          : "text-zinc-600 hover:text-zinc-900",
      )}
    >
      {icon || <FilterIcon size={14} className="md:w-4 md:h-4" />}
      <span>{tabName}</span>
      {count && (
        <span className="text-xs text-zinc-500 ml-1 hidden md:inline">({count})</span>
      )}
    </button>
  )
}
