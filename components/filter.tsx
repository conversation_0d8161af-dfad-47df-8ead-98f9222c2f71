"use client"

import { useRef, useEffect } from "react"
import type React from "react"
import {
  FilterIcon,
  Globe,
  Wrench,
  TrendingUp,
  Palette,
  Calendar,
  CalendarDays,
  Award,
  Video,
  Heart,
  Brush,
  Sparkles,
  Package,
  Code,
  Command,
} from "lucide-react"
import { categories } from "@/lib/constants"
import { cn } from "@/lib/utils"
import { BreathingLight } from "./BreathingLight";
import { CategoryTab } from "./CategoryTab";

interface FilterProps {
  selectedTab: string
  onTabChange: (tab: string) => void
  categoryCounts?: Record<string, number>
  onSearch?: (query: string) => void
}

export function Filter({ selectedTab, onTabChange, categoryCounts = {}, onSearch }: FilterProps) {
  const scrollRef = useRef<HTMLDivElement>(null)
  const activeTabRef = useRef<HTMLButtonElement>(null)

  // Scroll to center the active tab when it changes
  useEffect(() => {
    if (activeTabRef.current && scrollRef.current) {
      const container = scrollRef.current
      const activeTab = activeTabRef.current

      // Calculate the scroll position to center the active tab
      const scrollLeft = activeTab.offsetLeft - container.clientWidth / 2 + activeTab.clientWidth / 2

      // Smooth scroll to the position
      container.scrollTo({
        left: scrollLeft,
        behavior: "smooth",
      })
    }
  }, [selectedTab])

  // Update the categoryIcons object with the new icons
  const categoryIcons: Record<string, React.ReactNode> = {
    Winners: <Award size={16} />,
    Today: <CalendarDays size={16} />,
    AI: <Sparkles size={16} />,
    Design: <Palette size={16} />,
    Productivity: <Command size={16} />,
    Dev: <Code size={16} />,
    Marketing: <TrendingUp size={16} />,
    Video: <Video size={16} />,
    "Creative Services": <Brush size={16} />,
    "Cool Stuff": <Heart size={16} />,
    Others: <Package size={16} />,
    Remote: <Globe size={16} />,
    Tools: <Wrench size={16} />,
  }

  // 更新allTabs数组，添加Daily Product标签
  const allTabs = ["Winners", "Today", ...categories.filter((cat) => cat !== "All")]

  return (
    <div className="w-full overflow-x-auto scrollbar-hide px-2 md:px-0 -mx-2 md:mx-0" ref={scrollRef}>
      <div className="flex items-center border-b min-w-max px-2 md:px-0">
        {allTabs.map((tab) => {
          if (tab === "Winners" || tab === "Today") {
            // Keep existing rendering for Winners and Today
            return (
              <button
                key={tab}
                ref={selectedTab === tab ? activeTabRef : null}
                onClick={() => onTabChange(tab)}
                className={cn(
                  "flex items-center gap-1 md:gap-1.5 px-2 md:px-3 py-2 md:py-2.5 text-xs md:text-sm font-medium transition-colors relative",
                  selectedTab === tab
                    ? "text-zinc-900 border-b-2 border-zinc-900 -mb-px"
                    : "text-zinc-600 hover:text-zinc-900",
                )}
              >
                {categoryIcons[tab] || <FilterIcon size={14} className="md:w-4 md:h-4" />}
                <span>{tab}</span>
                {categoryCounts[tab] && (
                  <span className="text-xs text-zinc-500 ml-1 hidden md:inline">({categoryCounts[tab]})</span>
                )}
                {tab === "Today" && (
                  <BreathingLight className="absolute right-0.5 h-2 w-2" />
                )}
              </button>
            )
          } else {
            // Use CategoryTab for other categories
            return (
              <CategoryTab
                key={tab}
                ref={selectedTab === tab ? activeTabRef : undefined}
                tabName={tab}
                icon={categoryIcons[tab]}
                isSelected={selectedTab === tab}
                count={categoryCounts[tab]}
                onClick={() => onTabChange(tab)}
              />
            )
          }
        })}
      </div>
    </div>
  )
}
