"use client"

import { useState, useEffect } from "react"
import { User } from "@supabase/supabase-js"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card } from "@/components/ui/card"
import { BigCard } from "@/components/big-card"
import { mapSupabaseProduct } from "@/lib/supabase/types"
import type { SupabaseProduct } from "@/lib/supabase/types"
import { EmptyState } from "@/components/profile/empty-state"
import { useSearchParams, useRouter } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"

interface DynamicProfileTabsProps {
  user: User | null
  initialUpvotedProducts: SupabaseProduct[]
}

export function DynamicProfileTabs({ user, initialUpvotedProducts }: DynamicProfileTabsProps) {
  const searchParams = useSearchParams()
  const router = useRouter()
  const tabParam = searchParams.get('tab')
  const [activeTab, setActiveTab] = useState(tabParam || "upvoted")
  const [upvotedProducts, setUpvotedProducts] = useState<SupabaseProduct[]>(initialUpvotedProducts)
  const [isLoading, setIsLoading] = useState(false)

  // Update the URL when the tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    router.push(`/profile?tab=${value}`, { scroll: false })
  }

  // Update active tab when URL parameter changes
  useEffect(() => {
    if (tabParam && ['upvoted', 'submissions', 'settings'].includes(tabParam)) {
      setActiveTab(tabParam)
    }
  }, [tabParam])

  // Function to refresh upvoted products
  const refreshUpvotedProducts = async () => {
    if (!user) return

    setIsLoading(true)
    try {
      console.log("Refreshing upvoted products for user:", user.id)

      // Use API route to get upvoted products (bypasses RLS issues)
      const response = await fetch('/api/user/upvoted-products', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("API error:", errorData)
        throw new Error(errorData.error || 'Failed to fetch upvoted products')
      }

      const products = await response.json()
      console.log("Client - API response products:", products)

      // Convert to SupabaseProduct format if needed
      const supabaseProducts = products.map((product: any) => ({
        id: product.id,
        name: product.name,
        tagline: product.tagline,
        logo_url: product.logoUrl,
        cover_image_url: product.coverImageUrl,
        category: product.category,
        tags: product.tags,
        description: product.description,
        url: product.url,
        created_at: product.createdAt,
        updated_at: product.updatedAt,
        slug: product.slug,
      }))

      setUpvotedProducts(supabaseProducts)
    } catch (error) {
      console.error("Error refreshing upvoted products:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Refresh data when component mounts and when user changes
  useEffect(() => {
    if (user) {
      refreshUpvotedProducts()
    }
  }, [user])

  // Listen for storage events to refresh when votes change in other tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'vote_changed') {
        refreshUpvotedProducts()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [user])

  // Map Supabase products to the application's Product type
  const mappedProducts = upvotedProducts.map(mapSupabaseProduct)

  return (
    <Tabs value={activeTab} className="w-full" onValueChange={handleTabChange}>
      <TabsList className="mb-6">
        <TabsTrigger value="upvoted" className="relative px-4 py-2">
          Upvoted Products
          {mappedProducts.length > 0 && (
            <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-gray-100">
              {mappedProducts.length}
            </span>
          )}
        </TabsTrigger>
        <TabsTrigger value="submissions" className="relative px-4 py-2">
          Your Submissions
        </TabsTrigger>
        <TabsTrigger value="settings" className="relative px-4 py-2">
          Account Settings
        </TabsTrigger>
      </TabsList>

      <TabsContent value="upvoted" className="space-y-4">
        {isLoading ? (
          <div className="text-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading your upvoted products...</p>
          </div>
        ) : mappedProducts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mappedProducts.map((product) => (
              <BigCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <EmptyState
            title="No upvoted products yet"
            description="Products you upvote will appear here."
            actionText="Explore Products"
            actionLink="/"
          />
        )}

        {/* Debug info for development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-4 bg-gray-100 rounded-lg text-sm">
            <h4 className="font-medium mb-2">Debug Info:</h4>
            <p>User ID: {user?.id}</p>
            <p>Upvoted Products Count: {mappedProducts.length}</p>
            <p>Is Loading: {isLoading.toString()}</p>
            <button
              onClick={refreshUpvotedProducts}
              className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-xs"
            >
              Refresh Data
            </button>
          </div>
        )}
      </TabsContent>

      <TabsContent value="submissions" className="space-y-4">
        <EmptyState
          title="No submissions yet"
          description="Products you submit will appear here."
          actionText="Submit a Product"
          actionLink="https://tally.so/r/wLaKdp"
          isExternalLink
        />
      </TabsContent>

      <TabsContent value="settings" className="space-y-4">
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Account Settings</h3>
          <p className="text-muted-foreground mb-4">
            Manage your account settings and preferences.
          </p>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-1">Email</h4>
              <p className="text-sm text-muted-foreground">{user?.email}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-1">Account Created</h4>
              <p className="text-sm text-muted-foreground">
                {user?.created_at ? new Date(user.created_at).toLocaleDateString() : "N/A"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-1">Last Sign In</h4>
              <p className="text-sm text-muted-foreground">
                {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : "N/A"}
              </p>
            </div>
          </div>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
