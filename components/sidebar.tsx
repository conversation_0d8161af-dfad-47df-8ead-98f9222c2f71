import React from 'react';
import { Featured } from './featured';
import { Sponsor } from './sponsor'; // Import Sponsor component
import type { Product } from '@/lib/supabase/types'; // Import Product type

interface SidebarProps {
  className?: string;
  featuredProducts?: Product[]; // Add featuredProducts prop
  sponsoredProduct?: Product; // Add sponsoredProduct prop
}

export function Sidebar({ className, featuredProducts, sponsoredProduct }: SidebarProps) {
  return (
    <aside className={`w-full md:w-[26rem] lg:w-[30rem] space-y-6 ${className || ''}`}>
      {sponsoredProduct && <Sponsor product={sponsoredProduct} />} {/* Render Sponsor if product exists */}
      <Featured products={featuredProducts} /> {/* Pass products to Featured */}
      {/* Example: Another section in the sidebar */}
      {/* 
      <div className="p-4 border rounded-md bg-white">
        <h2 className="text-lg font-semibold mb-3">Categories</h2>
        <ul className="text-sm space-y-1">
          <li><a href="#" className="hover:underline">Category 1</a></li>
          <li><a href="#" className="hover:underline">Category 2</a></li>
          <li><a href="#" className="hover:underline">Category 3</a></li>
        </ul>
      </div>
      */}
    </aside>
  );
}
