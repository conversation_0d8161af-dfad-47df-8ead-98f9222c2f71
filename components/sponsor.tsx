import React from 'react';
import { BigCard } from '@/components/big-card';
import type { Product } from '@/lib/supabase/types';

interface SponsorProps {
  product: Product;
}

export function Sponsor({ product }: SponsorProps) {
  if (!product) {
    return null; // Don't render if no product is provided
  }

  return (
    <div className="mb-8">
      <h2 className="text-sm font-semibold mb-3 text-gray-500 uppercase tracking-wider">Sponsored</h2>
      <BigCard product={product} />
    </div>
  );
}
