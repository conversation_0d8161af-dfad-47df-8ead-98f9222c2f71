import { BigCard } from "@/components/big-card"
import { SmallCard } from "@/components/small-card"
import { TinyCard } from "@/components/tiny-card"
import type { Product } from "@/lib/supabase/types"
import { getProductsByIdsClient } from "@/lib/supabase/db"
import { useState, useEffect } from "react"

interface WinnersProps { // Renamed from HighlightProps
  // 新增直接接收产品数据的属性
  featuredProducts?: Product[]
  secondaryProducts?: Product[]
  tertiaryProducts?: Product[]
  // 保留旧的属性以保持兼容性
  products?: Product[]
  featuredIds?: number[]
  secondaryIds?: number[]
  tertiaryIds?: number[]
}

// 客户端版本的 Winners 组件 (Renamed from Highlight)
export function Winners({ // Renamed from Highlight
  // 新增直接接收产品数据的属性
  featuredProducts: initialFeaturedProducts,
  secondaryProducts: initialSecondaryProducts,
  tertiaryProducts: initialTertiaryProducts,
  // 保留旧的属性以保持兼容性
  products,
  featuredIds,
  secondaryIds,
  tertiaryIds
}: WinnersProps) { // Renamed from HighlightProps
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>(initialFeaturedProducts || [])
  const [secondaryProducts, setSecondaryProducts] = useState<Product[]>(initialSecondaryProducts || [])
  const [tertiaryProducts, setTertiaryProducts] = useState<Product[]>(initialTertiaryProducts || [])
  const [loading, setLoading] = useState(!initialFeaturedProducts)

  useEffect(() => {
    // 如果已经提供了直接的产品数据，则不需要再获取
    if (initialFeaturedProducts || initialSecondaryProducts || initialTertiaryProducts) {
      setLoading(false)
      return
    }

    const fetchProducts = async () => {
      setLoading(true)
      try {
        // 如果提供了产品 ID 数组，则获取指定 ID 的产品
        if (featuredIds || secondaryIds || tertiaryIds) {
          // 合并所有 ID 以便一次性获取
          const allIds = [...(featuredIds || []), ...(secondaryIds || []), ...(tertiaryIds || [])]

          if (allIds.length > 0) {
            const allProducts = await getProductsByIdsClient(allIds)

            // 根据 ID 数组过滤产品
            if (featuredIds && featuredIds.length > 0) {
              setFeaturedProducts(allProducts.filter(p => featuredIds.includes(Number(p.id))))
            }

            if (secondaryIds && secondaryIds.length > 0) {
              setSecondaryProducts(allProducts.filter(p => secondaryIds.includes(Number(p.id))))
            }

            if (tertiaryIds && tertiaryIds.length > 0) {
              setTertiaryProducts(allProducts.filter(p => tertiaryIds.includes(Number(p.id))))
            }
          }
        }
        // 如果没有提供 ID 数组但提供了产品数组，则使用切片方法
        else if (products && products.length > 0) {
          setFeaturedProducts(products.slice(0, 6))
          setSecondaryProducts(products.slice(6, 9))
          setTertiaryProducts(products.slice(9, 15))
        }
      } catch (error) {
        console.error("Error fetching products for Winners:", error) // Updated error message
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [products, featuredIds, secondaryIds, tertiaryIds, initialFeaturedProducts, initialSecondaryProducts, initialTertiaryProducts])

  if (loading) {
    return (
      <div className="space-y-12">
        {/* 骨架屏 - 保持与原始布局相同的结构 */}
        <section>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="space-y-3">
                <div className="aspect-[16/9] bg-muted animate-pulse rounded-md"></div>
                <div className="h-5 bg-muted animate-pulse rounded w-2/3"></div>
                <div className="h-4 bg-muted animate-pulse rounded w-full"></div>
              </div>
            ))}
          </div>
        </section>
      </div>
    )
  }

  return (
    <div className="space-y-12">
      {/* Featured section with BigCards */}
      {featuredProducts.length > 0 && (
        <section>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProducts.map((product) => (
              <BigCard key={product.id} product={product} />
            ))}
          </div>
        </section>
      )}

      {/* Secondary section with SmallCards */}
      {secondaryProducts.length > 0 && (
        <section>
          <h2 className="text-xl font-semibold mb-6">Worth Checking</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {secondaryProducts.map((product) => (
              <SmallCard key={product.id} product={product} />
            ))}
          </div>
        </section>
      )}

      {/* Tertiary section with TinyCards */}
      {tertiaryProducts.length > 0 && (
        <section>
          <h2 className="text-xl font-semibold mb-6">Quick Picks</h2>
          <div className="flex flex-wrap gap-2">
            {tertiaryProducts.map((product) => (
              <TinyCard key={product.id} product={product} />
            ))}
          </div>
        </section>
      )}

      <div className="flex justify-center">
        <div className="inline-flex items-center px-4 py-2 rounded-full bg-gray-100 text-sm font-medium">
          Curated selection of trending products
        </div>
      </div>
    </div>
  )
}
