"use client"

import { useState, useMemo } from "react"
import { Filter } from "@/components/filter"
import { ProductTimeline } from "@/components/product-timeline"
import { Today } from "@/components/today"
import { BigCard } from "@/components/big-card"
import { SmallCard } from "@/components/small-card"
import { Winners } from "@/components/winners"
import { Sidebar } from "@/components/sidebar"
import { getProductsByCategoryClient, searchProductsClient } from "@/lib/supabase/db"
import type { Product } from "@/lib/supabase/types"
import { highlightConfig } from "@/lib/config/site-config"

interface ClientHomeContentProps {
  initialProducts: Product[]
  initialFeaturedProducts?: Product[]
  initialSponsoredProduct?: Product
  initialSecondaryProducts?: Product[]
  initialTertiaryProducts?: Product[]
  categoryProducts?: Record<string, Product[]>
}

export function ClientHomeContent({
  initialProducts,
  initialFeaturedProducts,
  initialSponsoredProduct,
  initialSecondaryProducts,
  initialTertiaryProducts,
  categoryProducts = {}
}: ClientHomeContentProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts)
  const [loading, setLoading] = useState(false)
  const [selectedTab, setSelectedTab] = useState("Winners")
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest">("newest")

  // 计算分类计数
  const categoryCounts = useMemo(() => {
    const counts: Record<string, number> = { All: initialProducts.length }

    initialProducts.forEach((product) => {
      if (counts[product.category]) {
        counts[product.category]++
      } else {
        counts[product.category] = 1
      }
    })

    return counts
  }, [initialProducts])

  // 处理标签变化
  const handleTabChange = async (tab: string) => {
    setSelectedTab(tab)
    // "Today" and "Winners" use pre-loaded or initial products
    if (tab !== "Today" && tab !== "Winners") {
      setLoading(true)
      try {
        // 首先检查是否有预加载的分类数据
        let productsData: Product[] = []

        if (categoryProducts[tab] && categoryProducts[tab].length > 0) {
          // 使用预加载的数据
          productsData = categoryProducts[tab]
        } else {
          // 如果没有预加载数据，则从 API 获取
          productsData = await getProductsByCategoryClient(tab)
        }

        // 根据排序顺序对产品进行排序
        const sortedProducts = [...productsData].sort((a, b) => {
          const dateA = new Date(a.createdAt).getTime()
          const dateB = new Date(b.createdAt).getTime()
          return sortOrder === "newest" ? dateB - dateA : dateA - dateB
        })

        setProducts(sortedProducts)
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setLoading(false)
      }
    }
  }

  // 处理搜索
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      // 如果搜索为空，重置为选定的类别
      if (selectedTab !== "Today") {
        const productsData = await getProductsByCategoryClient(selectedTab)
        setProducts(productsData)
      }
      return
    }

    setLoading(true)
    try {
      const searchResults = await searchProductsClient(query)
      setProducts(searchResults)
      // 切换到网格视图以显示搜索结果
      if (selectedTab === "Today") {
        setSelectedTab("All")
      }
    } catch (error) {
      console.error("Error searching products:", error)
    } finally {
      setLoading(false)
    }
  }

  // 确定视图类型
  const isTodayView = selectedTab === "Today"

  return (
    <>
      <div className="w-full">
        <Filter
          selectedTab={selectedTab}
          onTabChange={handleTabChange}
          categoryCounts={categoryCounts}
          onSearch={handleSearch}
        />
      </div>

      <div className="mt-8">
        {loading ? (
          isTodayView ? (
            // Updated skeleton for two-column layout (main content part)
            <div className="flex flex-col md:flex-row gap-8">
              <div className="flex-1 space-y-12">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="space-y-4">
                    <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      {[1, 2].map((j) => (
                        <div key={j} className="bg-gray-200 rounded-lg p-4 space-y-3">
                          <div className="h-32 bg-gray-300 rounded"></div>
                          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                          <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                          <div className="h-8 bg-gray-300 rounded w-1/4 mt-2"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              {/* Skeleton for Sidebar */}
              <div className="hidden md:block md:w-[28rem] lg:w-[32rem] space-y-6">
                {/* Skeleton for Sponsor */}
                <div className="mb-8">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-3"></div> {/* Sponsored title placeholder */}
                  <div className="bg-gray-200 rounded-lg p-4 space-y-3"> {/* BigCard placeholder */}
                    <div className="aspect-[16/9] w-full bg-gray-300 rounded"></div>
                    <div className="h-5 bg-gray-300 rounded w-1/2 mt-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-3/4 mt-1"></div>
                  </div>
                </div>
                {/* Skeleton for Featured */}
                <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div> {/* Featured title placeholder */}
                {[1, 2, 3].map((i) => (
                  <div key={i} className="bg-gray-200 rounded-lg p-3 mb-4 space-y-2"> {/* SmallCard placeholder for Featured items */}
                    <div className="flex items-center space-x-3">
                      <div className="h-16 w-16 bg-gray-300 rounded"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                      </div>
                    </div>
                    <div className="h-6 bg-gray-300 rounded w-1/4 mt-1"></div> {/* Vote button placeholder */}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            // 网格加载骨架屏
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="space-y-3">
                  <div className="aspect-[16/9] bg-muted animate-pulse rounded-md"></div>
                  <div className="h-5 bg-muted animate-pulse rounded w-2/3"></div>
                  <div className="h-4 bg-muted animate-pulse rounded w-full"></div>
                </div>
              ))}
            </div>
          )
        ) : selectedTab === "Winners" ? (
          <Winners
            featuredProducts={initialFeaturedProducts}
            secondaryProducts={initialSecondaryProducts}
            tertiaryProducts={initialTertiaryProducts}
          />
        ) : isTodayView ? (
          <div className="flex flex-col md:flex-row gap-8">
            <main className="flex-1">
              <Today products={initialProducts} />
            </main>
            <Sidebar 
              className="hidden md:block"
              featuredProducts={initialFeaturedProducts}
              sponsoredProduct={initialSponsoredProduct}
            />
          </div>
        ) : (
          // OTHER CATEGORY VIEWS (NO SIDEBAR RENDERED HERE BY ClientHomeContent)
          // Explicitly wrap in a div that takes full width of its container
          <div className="w-full">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {products.map((product) => (
                <BigCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  )
}
