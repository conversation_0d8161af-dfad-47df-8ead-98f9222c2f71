import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient, createSupabaseAdminClient } from "@/lib/supabase/client"
import { mapSupabaseProduct } from "@/lib/supabase/types"

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient()

    // Get the current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      console.error("Session error:", sessionError)
      return NextResponse.json({ error: "Authentication error" }, { status: 401 })
    }

    if (!session?.user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    console.log("API - Getting upvoted products for user:", session.user.id)

    // Get user's upvoted products
    const { data: userVotes, error: votesError } = await supabase
      .from("user_votes")
      .select("product_id, vote_type, created_at")
      .eq("user_id", session.user.id)
      .eq("vote_type", "upvote")
      .order("created_at", { ascending: false })

    console.log("API - User votes:", userVotes)
    console.log("API - Votes error:", votesError)

    if (votesError) {
      console.error("Error fetching user votes:", votesError)
      return NextResponse.json({ error: "Failed to fetch votes" }, { status: 500 })
    }

    // Get product details
    const upvotedProductIds = userVotes?.map(vote => vote.product_id) || []

    console.log("API - Upvoted product IDs:", upvotedProductIds)

    if (upvotedProductIds.length === 0) {
      return NextResponse.json([])
    }

    // Use admin client for product data to ensure we can read all products
    const adminSupabase = createSupabaseAdminClient()
    const { data: products, error: productsError } = await adminSupabase
      .from("products")
      .select("*")
      .in("id", upvotedProductIds)
      .order("created_at", { ascending: false })

    console.log("API - Products:", products)
    console.log("API - Products error:", productsError)

    if (productsError) {
      console.error("Error fetching products:", productsError)
      return NextResponse.json({ error: "Failed to fetch products" }, { status: 500 })
    }

    // Map to application format
    const mappedProducts = (products || []).map(mapSupabaseProduct)

    console.log("API - Final mapped products:", mappedProducts.length)

    return NextResponse.json(mappedProducts)

  } catch (error) {
    console.error("Error in upvoted products API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
