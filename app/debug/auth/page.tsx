import { createSupabaseServerClient } from "@/lib/supabase/client"

export default async function AuthDebugPage() {
  const supabase = createSupabaseServerClient()

  // Get session and user data
  const { data: { session }, error: sessionError } = await supabase.auth.getSession()
  const { data: { user }, error: userError } = await supabase.auth.getUser()

  // Get user votes if user exists
  let userVotes = null
  let votesError = null
  
  if (user) {
    const { data, error } = await supabase
      .from("user_votes")
      .select("product_id, vote_type, created_at")
      .eq("user_id", user.id)
      .eq("vote_type", "upvote")
      .order("created_at", { ascending: false })
    
    userVotes = data
    votesError = error
  }

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Session Data</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              hasSession: !!session,
              sessionError: sessionError?.message,
              user: session?.user ? {
                id: session.user.id,
                email: session.user.email,
                created_at: session.user.created_at
              } : null
            }, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">User Data</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              hasUser: !!user,
              userError: userError?.message,
              user: user ? {
                id: user.id,
                email: user.email,
                created_at: user.created_at
              } : null
            }, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">User Votes</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              votesCount: userVotes?.length || 0,
              votesError: votesError?.message,
              votes: userVotes
            }, null, 2)}
          </pre>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h2 className="text-lg font-semibold mb-2 text-blue-800">Summary</h2>
          <div className="text-blue-700">
            <p><strong>Authentication Status:</strong> {user ? "✅ Logged in" : "❌ Not logged in"}</p>
            <p><strong>User Email:</strong> {user?.email || "N/A"}</p>
            <p><strong>Vote Count:</strong> {userVotes?.length || 0}</p>
            <p><strong>Profile Should Show:</strong> {userVotes?.length ? `${userVotes.length} upvoted products` : "Empty state"}</p>
          </div>
        </div>

        <div className="mt-6">
          <a 
            href="/profile" 
            className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go to Profile Page
          </a>
        </div>
      </div>
    </div>
  )
}
