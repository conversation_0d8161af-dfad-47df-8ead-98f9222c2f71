import { cache } from "react"
import { createSupabaseAdminClient } from "./client"
import { mapSupabaseProduct } from "./types"
import type { Product } from "@/lib/supabase/types"

// 使用 React 的 cache 函数缓存数据获取结果
export const getProducts = cache(async (): Promise<Product[]> => {
  const supabase = createSupabaseAdminClient()

  const { data, error } = await supabase.from("products").select("*").order("created_at", { ascending: false })

  if (error) {
    console.error("Error fetching products:", error)
    return []
  }

  return data.map(mapSupabaseProduct)
})

export const getProductsByCategory = cache(async (category: string): Promise<Product[]> => {
  const supabase = createSupabaseAdminClient()

  if (category === "All") {
    return getProducts()
  }

  const { data, error } = await supabase
    .from("products")
    .select("*")
    .eq("category", category)
    .order("created_at", { ascending: false })

  if (error) {
    console.error(`Error fetching products by category ${category}:`, error)
    return []
  }

  return data.map(mapSupabaseProduct)
})

export const searchProducts = cache(async (query: string): Promise<Product[]> => {
  const supabase = createSupabaseAdminClient()
  const lowercaseQuery = query.toLowerCase()

  const { data, error } = await supabase
    .from("products")
    .select("*")
    .or(`name.ilike.%${lowercaseQuery}%,tagline.ilike.%${lowercaseQuery}%,description.ilike.%${lowercaseQuery}%`)

  if (error) {
    console.error(`Error searching products for "${query}":`, error)
    return []
  }

  return data.map(mapSupabaseProduct)
})

export const getRecentProducts = cache(async (limit = 5): Promise<Product[]> => {
  const supabase = createSupabaseAdminClient()

  const { data, error } = await supabase
    .from("products")
    .select("*")
    .order("created_at", { ascending: false })
    .limit(limit)

  if (error) {
    console.error(`Error fetching recent products:`, error)
    return []
  }

  return data.map(mapSupabaseProduct)
})

export const getProductById = cache(async (id: string | number): Promise<Product | null> => {
  const supabase = createSupabaseAdminClient()

  const { data, error } = await supabase.from("products").select("*").eq("id", id.toString()).single()

  if (error) {
    console.error(`Error fetching product with ID ${id}:`, error)
    return null
  }

  return mapSupabaseProduct(data)
})

export const getProductBySlug = cache(async (slug: string): Promise<Product | null> => {
  const supabase = createSupabaseAdminClient()

  const { data, error } = await supabase.from("products").select("*").eq("slug", slug).single()

  if (error) {
    console.error(`Error fetching product with slug ${slug}:`, error)
    return null
  }

  return mapSupabaseProduct(data)
})

export const getProductsByIds = cache(async (ids: number[]): Promise<Product[]> => {
  if (!ids || ids.length === 0) {
    return []
  }

  const supabase = createSupabaseAdminClient()

  const { data, error } = await supabase
    .from("products")
    .select("*")
    .in("id", ids)
    .order("created_at", { ascending: false })

  if (error) {
    console.error(`Error fetching products with IDs ${ids.join(", ")}:`, error)
    return []
  }

  return data.map(mapSupabaseProduct)
})

// 客户端版本的 getProductsByIds 函数
export const getProductsByIdsClient = async (ids: number[]): Promise<Product[]> => {
  if (!ids || ids.length === 0) {
    return []
  }

  try {
    const response = await fetch(`/api/products/by-ids?ids=${ids.join(",")}`)
    if (!response.ok) {
      throw new Error(`Error fetching products: ${response.statusText}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching products by IDs:", error)
    return []
  }
}

// 客户端版本的 getProductsByCategory 函数
export const getProductsByCategoryClient = async (category: string): Promise<Product[]> => {
  try {
    const response = await fetch(`/api/products/by-category?category=${encodeURIComponent(category)}`)
    if (!response.ok) {
      throw new Error(`Error fetching products by category: ${response.statusText}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error(`Error fetching products by category ${category}:`, error)
    return []
  }
}

// 客户端版本的 searchProducts 函数
export const searchProductsClient = async (query: string): Promise<Product[]> => {
  try {
    const response = await fetch(`/api/products/search?query=${encodeURIComponent(query)}`)
    if (!response.ok) {
      throw new Error(`Error searching products: ${response.statusText}`)
    }
    const data = await response.json()
    return data
  } catch (error) {
    console.error(`Error searching products for "${query}":`, error)
    return []
  }
}

export const getProductsByDate = cache(async (date: string): Promise<Product[]> => {
  const supabase = createSupabaseAdminClient()

  // 转换日期为 ISO 格式的开始和结束时间
  const startDate = new Date(date)
  startDate.setUTCHours(0, 0, 0, 0)

  const endDate = new Date(date)
  endDate.setUTCHours(23, 59, 59, 999)

  const { data, error } = await supabase
    .from("products")
    .select("*")
    .gte("created_at", startDate.toISOString())
    .lte("created_at", endDate.toISOString())

  if (error) {
    console.error(`Error fetching products for date ${date}:`, error)
    return []
  }

  return data.map(mapSupabaseProduct)
})

export const getProductDates = cache(async (): Promise<string[]> => {
  const supabase = createSupabaseAdminClient()

  // 获取所有产品的创建日期
  const { data, error } = await supabase.from("products").select("created_at")

  if (error) {
    console.error("Error fetching product dates:", error)
    return []
  }

  // 提取唯一日期（仅保留日期部分）
  const uniqueDates = new Set<string>()
  data.forEach((product) => {
    const date = new Date(product.created_at)
    const dateString = date.toISOString().split("T")[0] // 格式为 YYYY-MM-DD
    uniqueDates.add(dateString)
  })

  // 转换为数组并按降序排序（最新的优先）
  return Array.from(uniqueDates).sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
})

// 获取相关产品 - 混合推荐策略（基于分类和标签）
export const getRelatedProducts = cache(async (product: Product, limit = 6): Promise<Product[]> => {
  const supabase = createSupabaseAdminClient()

  if (!product) {
    return []
  }

  // 1. 获取所有产品（除了当前产品）
  const { data: allProducts, error } = await supabase
    .from("products")
    .select("*")
    .neq("id", product.id)
    .order("created_at", { ascending: false })

  if (error || !allProducts) {
    console.error("Error fetching products for recommendations:", error)
    return []
  }

  // 将数据转换为应用数据模型
  const products = allProducts.map(mapSupabaseProduct)

  // 2. 计算每个产品的相关性分数
  const scoredProducts = products.map(p => {
    let score = 0

    // 相同分类的产品获得高分
    if (p.category === product.category) {
      score += 10
    }

    // 共享标签的产品获得额外分数（每个共享标签加1分）
    if (product.tags && product.tags.length > 0 && p.tags && p.tags.length > 0) {
      const sharedTags = product.tags.filter(tag => p.tags.includes(tag))
      score += sharedTags.length * 2
    }

    return { product: p, score }
  })

  // 3. 按分数排序并返回前N个产品
  return scoredProducts
    .sort((a, b) => b.score - a.score) // 按分数降序排序
    .filter(item => item.score > 0)    // 只返回有相关性的产品
    .slice(0, limit)                   // 限制结果数量
    .map(item => item.product)         // 只返回产品对象
})

// 客户端版本的产品数据预加载函数
export async function prefetchProductDataClient(id: string | number): Promise<void> {
  // 这个函数是为了在客户端使用的，它不会抛出错误
  try {
    // 在客户端，我们只预加载路由，不预加载数据
    // 这是因为客户端的 Supabase 客户端没有足够的权限来执行某些查询
    console.log(`Client-side prefetching for product ${id} (route only)`)
    // 不执行实际的数据获取，避免客户端错误
  } catch (error) {
    console.error("Error in client-side prefetch:", error)
  }
}

// 预获取产品数据的函数
export async function prefetchProductData(id: string | number): Promise<void> {
  try {
    // 预获取产品详情
    const product = await getProductById(id)

    if (product) {
      // 预获取相关产品
      await getProductsByCategory(product.category)
    }
  } catch (error) {
    console.error("Error prefetching product data:", error)
  }
}
